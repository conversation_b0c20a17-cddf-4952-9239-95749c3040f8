#!/usr/bin/env python3
"""
测试Media模型集成
验证aicrawl模块是否正确使用Media模型
"""

import os
import sys
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import get_database_manager, init_database

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    try:
        # 初始化数据库
        success = init_database()
        if success:
            print("✅ 数据库初始化成功")
        else:
            print("❌ 数据库初始化失败")
            return False
        
        # 获取数据库管理器
        db_manager = get_database_manager()
        if db_manager and db_manager.initialized:
            print("✅ 数据库管理器获取成功")
        else:
            print("❌ 数据库管理器获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def test_media_model():
    """测试Media模型"""
    print("\n" + "=" * 50)
    print("测试Media模型")
    print("=" * 50)
    
    try:
        db_manager = get_database_manager()
        if not db_manager or not db_manager.initialized:
            print("❌ 数据库未初始化")
            return False
        
        # 测试数据
        test_articles = [
            {
                'title': '测试雪球文章1',
                'url': 'https://xueqiu.com/test1',
                'content': '这是一篇测试文章的内容',
                'author': '测试作者',
                'publish_time': '2024-01-01 10:00:00',
                'important': True
            },
            {
                'title': '测试雪球文章2',
                'url': 'https://xueqiu.com/test2',
                'content': '这是另一篇测试文章的内容',
                'author': '测试作者2',
                'publish_time': '2024-01-01 11:00:00',
                'important': False
            }
        ]
        
        # 保存测试数据
        print("保存测试文章...")
        stats = db_manager.save_articles(test_articles)
        print(f"保存结果: 成功 {stats['success']} 篇，失败 {stats['failed']} 篇，跳过 {stats['skipped']} 篇")
        
        if stats['success'] > 0:
            print("✅ 文章保存成功")
        else:
            print("❌ 文章保存失败")
            return False
        
        # 获取文章数量
        count = db_manager.get_article_count()
        print(f"数据库中雪球文章总数: {count}")
        
        # 获取最近文章
        recent_articles = db_manager.get_recent_articles(5)
        print(f"最近文章数量: {len(recent_articles)}")
        
        if recent_articles:
            print("最近文章:")
            for i, article in enumerate(recent_articles[:3], 1):
                print(f"  {i}. {article['title'][:50]}...")
                print(f"     来源: {article['source']}, ID: {article['mediaId'][:20]}...")
        
        print("✅ Media模型测试成功")
        return True
        
    except Exception as e:
        print(f"❌ Media模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_mapping():
    """测试字段映射"""
    print("\n" + "=" * 50)
    print("测试字段映射")
    print("=" * 50)
    
    try:
        db_manager = get_database_manager()
        
        # 测试字段长度限制
        test_article = {
            'title': 'A' * 150,  # 超过120字符限制
            'url': 'https://xueqiu.com/' + 'B' * 250,  # 超过200字符限制
            'content': 'C' * 1000,  # 长内容
            'publish_time': '2024-01-01 12:00:00',
            'important': True
        }
        
        # 转换数据
        converted = db_manager._convert_to_db_model(test_article)
        
        if converted:
            print("✅ 数据转换成功")
            print(f"标题长度: {len(converted['title'])} (限制: 120)")
            print(f"URL长度: {len(converted['url'])} (限制: 200)")
            print(f"时间格式: {converted['createTime']} (限制: 10)")
            print(f"来源: {converted['source']} (限制: 8)")
            
            # 验证长度限制
            if len(converted['title']) <= 120:
                print("✅ 标题长度限制正确")
            else:
                print("❌ 标题长度限制失败")
                
            if len(converted['url']) <= 200:
                print("✅ URL长度限制正确")
            else:
                print("❌ URL长度限制失败")
                
            if len(converted['createTime']) <= 10:
                print("✅ 时间长度限制正确")
            else:
                print("❌ 时间长度限制失败")
                
            return True
        else:
            print("❌ 数据转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 字段映射测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试aicrawl Media模型集成")
    print("时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 运行测试
    tests = [
        ("数据库连接", test_database_connection),
        ("Media模型", test_media_model),
        ("字段映射", test_field_mapping),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！aicrawl已成功集成Media模型")
    else:
        print("⚠️  部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
