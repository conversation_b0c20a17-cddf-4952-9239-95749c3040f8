#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表和初始化数据
"""

import os
import sys
import argparse
import logging
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import init_database, get_database_manager
from db_config import get_database_url

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_database_if_not_exists(database_url: str) -> bool:
    """创建数据库（如果不存在）"""
    try:
        from sqlalchemy import create_engine, text
        from urllib.parse import urlparse
        
        # 解析数据库URL
        parsed = urlparse(database_url)
        
        if parsed.scheme.startswith('mysql'):
            # MySQL数据库
            db_name = parsed.path.lstrip('/')
            
            # 创建不包含数据库名的连接URL
            base_url = f"{parsed.scheme}://{parsed.netloc}/"
            if parsed.query:
                base_url += f"?{parsed.query}"
            
            # 连接到MySQL服务器
            engine = create_engine(base_url)
            
            with engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(text(f"SHOW DATABASES LIKE '{db_name}'"))
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(text(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    logger.info(f"数据库 {db_name} 创建成功")
                else:
                    logger.info(f"数据库 {db_name} 已存在")
            
            engine.dispose()
            return True
            
        elif parsed.scheme.startswith('sqlite'):
            # SQLite数据库会自动创建
            logger.info("SQLite数据库将自动创建")
            return True
        else:
            logger.warning(f"不支持的数据库类型: {parsed.scheme}")
            return False
            
    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        return False

def init_tables(database_url: str = None) -> bool:
    """初始化数据库表"""
    try:
        logger.info("开始初始化数据库表...")
        
        # 初始化数据库
        success = init_database(database_url)
        if not success:
            logger.error("数据库初始化失败")
            return False
        
        # 获取数据库管理器
        db_manager = get_database_manager()
        if not db_manager or not db_manager.initialized:
            logger.error("数据库管理器初始化失败")
            return False
        
        logger.info("数据库表初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"初始化数据库表失败: {e}")
        return False

def show_database_info(database_url: str = None) -> None:
    """显示数据库信息"""
    try:
        # 初始化数据库
        success = init_database(database_url)
        if not success:
            print("❌ 数据库连接失败")
            return
        
        # 获取数据库管理器
        db_manager = get_database_manager()
        if not db_manager or not db_manager.initialized:
            print("❌ 数据库管理器初始化失败")
            return
        
        # 获取统计信息
        total_count = db_manager.get_article_count()
        recent_articles = db_manager.get_recent_articles(5)
        
        print("\n=== 数据库信息 ===")
        print(f"📊 文章总数: {total_count}")
        
        if recent_articles:
            print(f"\n📰 最近 {len(recent_articles)} 篇文章:")
            for i, article in enumerate(recent_articles, 1):
                print(f"  {i}. {article.get('title', 'N/A')[:60]}...")
                print(f"     作者: {article.get('author', 'N/A')} | 时间: {article.get('crawl_time', 'N/A')}")
                print()
        else:
            print("\n📰 暂无文章数据")
            
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        print(f"❌ 获取数据库信息失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库初始化工具')
    parser.add_argument('--database-url', type=str,
                       help='数据库连接URL')
    parser.add_argument('--create-db', action='store_true',
                       help='创建数据库（如果不存在）')
    parser.add_argument('--init-tables', action='store_true',
                       help='初始化数据库表')
    parser.add_argument('--show-info', action='store_true',
                       help='显示数据库信息')
    parser.add_argument('--db-type', choices=['mysql', 'sqlite'], default='mysql',
                       help='数据库类型')
    
    args = parser.parse_args()
    
    # 获取数据库URL
    if args.database_url:
        database_url = args.database_url
    else:
        try:
            database_url = get_database_url(args.db_type)
        except Exception as e:
            logger.error(f"获取数据库URL失败: {e}")
            return
    
    print(f"数据库URL: {database_url}")
    
    # 执行操作
    if args.create_db:
        print("\n创建数据库...")
        success = create_database_if_not_exists(database_url)
        if success:
            print("✅ 数据库创建成功")
        else:
            print("❌ 数据库创建失败")
            return
    
    if args.init_tables:
        print("\n初始化数据库表...")
        success = init_tables(database_url)
        if success:
            print("✅ 数据库表初始化成功")
        else:
            print("❌ 数据库表初始化失败")
            return
    
    if args.show_info:
        show_database_info(database_url)
    
    # 如果没有指定任何操作，显示帮助
    if not any([args.create_db, args.init_tables, args.show_info]):
        print("\n=== 数据库初始化工具 ===")
        print("使用示例:")
        print("  python init_database.py --create-db --init-tables    # 创建数据库和表")
        print("  python init_database.py --show-info                  # 显示数据库信息")
        print("  python init_database.py --database-url mysql://...   # 使用自定义数据库URL")
        print("\n环境变量配置:")
        print("  MYSQL_HOST=localhost")
        print("  MYSQL_PORT=3306")
        print("  MYSQL_USER=root")
        print("  MYSQL_PASSWORD=your_password")
        print("  MYSQL_DATABASE=xueqiu_crawler")

if __name__ == "__main__":
    main()
