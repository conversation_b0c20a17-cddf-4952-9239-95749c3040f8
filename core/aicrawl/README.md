# 热门文章爬虫

专门爬取各个页面热门文章的智能爬虫系统，集成了完整的反检测功能和智能数据提取策略。

## 🚀 核心特性

### 反爬虫绕过功能
- **智能User-Agent轮换**: 模拟真实浏览器访问
- **动态请求头管理**: 随机化请求头，避免指纹识别
- **频率控制系统**: 智能延迟控制，模拟人类访问模式
- **代理池管理**: 支持代理轮换和失败检测
- **浏览器指纹伪装**: 完整的浏览器环境模拟
- **自适应重试机制**: 智能错误处理和重试策略

### 热门文章提取功能
- **多页面支持**: 同时爬取多个页面的热门内容
- **智能去重**: 自动识别和去除重复文章
- **多类型提取**: 支持热门话题、用户文章、趋势内容
- **结构化输出**: 标准化的文章数据格式
- **增强模式**: 自动展开内容和滚动加载更多文章

### 监控和优化
- **实时性能监控**: 成功率、响应时间统计
- **自适应调整**: 根据成功率动态调整策略
- **详细日志记录**: 完整的爬取过程记录

## 📁 项目结构

```
core/aicrawl/
├── main_advanced.py               # 主程序入口
├── crawler_engine.py              # 爬虫引擎核心
├── extractors.py                  # 数据提取器
├── anti_detection.py              # 反检测模块
├── config.py                      # 配置文件
├── monitoring.py                  # 性能监控模块
├── requirements.txt               # 依赖包列表
└── README.md                      # 项目说明
```

## 🛠 环境搭建

1. **安装Python依赖**
```bash
cd /Users/<USER>/Repo/Blackbear/core/aicrawl
pip install -r requirements.txt
```

2. **安装浏览器依赖**
```bash
python -m playwright install --with-deps chromium
```

3. **验证安装**
```bash
crawl4ai-doctor
```

## 🎯 使用方法

### 快速开始 - 演示模式
```bash
python main_advanced.py
```
这将运行演示模式，爬取默认页面的热门文章。

### 指定页面爬取
```bash
# 爬取指定页面的热门文章
python main_advanced.py --pages https://xueqiu.com

# 爬取多个页面
python main_advanced.py --pages https://xueqiu.com https://xueqiu.com/today
```

### 保存结果到文件
```bash
# 保存到指定文件
python main_advanced.py --output my_articles.json

# 指定页面并保存
python main_advanced.py --pages https://xueqiu.com --output articles.json
```

### 高级参数
```bash
python main_advanced.py \
  --pages https://xueqiu.com https://xueqiu.com/today \
  --output my_articles.json \
  --disable-anti-detection \
  --enhanced
```

### 参数说明
- `--pages`: 指定要爬取的页面URL列表（可选，默认使用预设页面）
- `--output`: 输出文件名（可选，默认自动生成）
- `--disable-anti-detection`: 禁用反检测功能（可选）
- `--enhanced`: 启用增强模式，自动展开和滚动加载（默认启用）

## ⚙️ 配置说明

### 代理配置
编辑 `config.py` 中的 `PROXY_POOLS`：
```python
PROXY_POOLS = [
    {"http": "http://proxy1:port", "https": "https://proxy1:port"},
    {"http": "http://proxy2:port", "https": "https://proxy2:port"},
]
```

### 频率控制
调整 `REQUEST_DELAYS` 参数：
```python
REQUEST_DELAYS = {
    "min_delay": 2,      # 最小延迟(秒)
    "max_delay": 8,      # 最大延迟(秒)
    "burst_delay": 15,   # 突发请求后的延迟(秒)
    "error_delay": 30    # 错误后的延迟(秒)
}
```

## 📊 输出格式

### JSON格式
```json
[
  {
    "type": "hot_topic",
    "title": "算力硬件股拉升，胜宏科技历史新高",
    "author": "",
    "url": "https://xueqiu.com/hashtag/...",
    "view_count": null,
    "comment_count": null,
    "source_page": "https://xueqiu.com",
    "crawl_time": "2025-08-25T19:27:17.709427"
  },
  {
    "type": "user_post",
    "title": "今日市场分析...",
    "content": "详细的市场分析内容...",
    "author": "投资达人",
    "url": "https://xueqiu.com/...",
    "like_count": 123,
    "comment_count": 45,
    "publish_time": "2025-08-25T18:30:00",
    "mentioned_stocks": ["SH000001", "SZ000002"],
    "source_page": "https://xueqiu.com",
    "crawl_time": "2025-08-25T19:27:17.709427"
  }
]
```

### CSV格式
自动生成CSV文件，包含所有成功爬取的文章数据。

## 🔧 API使用

### 编程接口
```python
from main_advanced import PopularArticlesCrawler

# 创建爬虫实例
crawler = PopularArticlesCrawler(enable_anti_detection=True)

# 爬取热门文章
articles = await crawler.crawl_popular_articles(
    pages=["https://xueqiu.com", "https://xueqiu.com/today"],
    enhanced_mode=True
)

# 保存结果
crawler.save_results(articles, "my_articles.json")

# 获取性能报告
report = crawler.crawler.get_performance_report()
```

## 🛡️ 反爬虫策略详解

### 1. 浏览器指纹伪装
- 随机User-Agent轮换
- 真实浏览器参数模拟
- 禁用自动化检测标识

### 2. 请求模式优化
- 智能频率控制
- 随机延迟注入
- 人类行为模拟

### 3. 网络层保护
- 代理池轮换
- IP失败检测
- 自动重试机制

### 4. 数据提取策略
- JavaScript变量提取
- DOM元素备用提取
- 多重数据验证

## 📈 性能优化

### 并发控制
- 默认并发数：3
- 可根据网络情况调整
- 自动负载均衡

### 内存管理
- 流式数据处理
- 及时释放资源
- 批量结果保存

### 错误恢复
- 智能重试机制
- 代理自动切换
- 降级策略支持

## ⚠️ 注意事项

1. **合规使用**
   - 遵守网站robots.txt规则
   - 控制爬取频率，避免对服务器造成压力
   - 仅用于学习和研究目的

2. **代理配置**
   - 建议使用高质量代理
   - 定期更新代理池
   - 监控代理可用性

3. **数据准确性**
   - 股票数据仅供参考
   - 建议与官方数据对比验证
   - 注意市场开闭盘时间

4. **系统资源**
   - 监控CPU和内存使用
   - 适当调整并发数
   - 定期清理日志文件

## 🔍 故障排除

### 常见问题

1. **爬取失败率高**
   - 检查网络连接
   - 更新代理池
   - 降低并发数

2. **数据提取不完整**
   - 检查网站结构变化
   - 更新选择器配置
   - 启用详细日志

3. **性能问题**
   - 调整延迟参数
   - 优化并发设置
   - 检查系统资源

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from test_crawler import test_basic_crawl
import asyncio
asyncio.run(test_basic_crawl())
"
```

## 📝 更新日志

### v2.1.0 (当前版本)
- ✅ 完整的反爬虫架构
- ✅ 智能数据提取策略
- ✅ 性能监控和自适应调整
- ✅ 批量爬取支持
- ✅ 详细的错误处理
- ✅ **评论数据爬取**: 从HTML提取评论，确保与API格式一致
- ✅ **多层数据提取**: JavaScript变量 + DOM结构双重提取
- ✅ **数据结构验证**: 自动验证API兼容性

### v2.0.0 (上一版本)
- ✅ 完整的反爬虫架构
- ✅ 智能数据提取策略
- ✅ 性能监控和自适应调整

### v1.0.0 (原始版本)
- ✅ 基础crawl4ai集成
- ✅ 简单数据提取

## 📚 专项功能文档

- **[评论爬取指南](COMMENTS_CRAWLING_GUIDE.md)** - 详细的评论数据爬取说明
- **[增强功能说明](ENHANCED_FEATURES.md)** - 系统增强功能介绍
- **[HTML爬取指南](HTML_CRAWLING_GUIDE.md)** - HTML页面爬取技术
- **[升级总结](UPGRADE_SUMMARY.md)** - 系统升级历程

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。