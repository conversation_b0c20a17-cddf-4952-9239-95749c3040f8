#!/usr/bin/env python3
"""
测试爬取速度优化
对比普通模式和快速模式的性能差异
"""

import asyncio
import sys
import os
import time
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_link_extraction_speed():
    """测试链接提取速度"""
    print("=" * 60)
    print("测试链接提取速度")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        # 创建爬虫实例
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        # 测试链接提取速度
        start_time = time.time()
        links = await crawler._extract_article_links_from_page("https://xueqiu.com/today")
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"链接提取完成:")
        print(f"  提取到链接数量: {len(links)}")
        print(f"  耗时: {duration:.2f} 秒")
        print(f"  平均速度: {len(links)/duration:.2f} 链接/秒")
        
        if links:
            print(f"  前5个链接示例:")
            for i, link in enumerate(links[:5], 1):
                print(f"    {i}. {link}")
        
        return len(links), duration
        
    except Exception as e:
        print(f"❌ 链接提取速度测试失败: {e}")
        return 0, 0

async def test_batch_crawl_speed(fast_mode=False):
    """测试批次爬取速度"""
    mode_name = "快速模式" if fast_mode else "普通模式"
    print(f"\n{'=' * 60}")
    print(f"测试批次爬取速度 - {mode_name}")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        # 创建爬虫实例
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        # 准备测试URL（使用一些示例URL）
        test_urls = [
            "https://xueqiu.com/1234567890/123456789",
            "https://xueqiu.com/2345678901/234567890",
            "https://xueqiu.com/3456789012/345678901",
            "https://xueqiu.com/4567890123/456789012",
            "https://xueqiu.com/5678901234/567890123",
        ]
        
        visited_urls = set()
        
        print(f"开始{mode_name}批次爬取测试...")
        print(f"测试URL数量: {len(test_urls)}")
        print(f"并发限制: {'20' if fast_mode else '10'}")
        
        start_time = time.time()
        results = await crawler._crawl_articles_batch(
            test_urls, 
            fetch_full_content=False,  # 不获取完整内容以加快测试
            visited_urls=visited_urls,
            fast_mode=fast_mode
        )
        end_time = time.time()
        
        duration = end_time - start_time
        success_count = sum(1 for result in results if result[0] is not None)
        
        print(f"\n{mode_name}批次爬取完成:")
        print(f"  处理URL数量: {len(test_urls)}")
        print(f"  成功爬取数量: {success_count}")
        print(f"  总耗时: {duration:.2f} 秒")
        print(f"  平均速度: {len(test_urls)/duration:.2f} URL/秒")
        print(f"  成功率: {success_count/len(test_urls)*100:.1f}%")
        
        return success_count, duration
        
    except Exception as e:
        print(f"❌ {mode_name}批次爬取测试失败: {e}")
        return 0, 0

async def test_mini_deep_crawl(fast_mode=False):
    """测试小规模深度爬取"""
    mode_name = "快速模式" if fast_mode else "普通模式"
    print(f"\n{'=' * 60}")
    print(f"测试小规模深度爬取 - {mode_name}")
    print("=" * 60)
    
    try:
        from main_advanced import PopularArticlesCrawler
        
        # 创建爬虫实例
        crawler = PopularArticlesCrawler(save_to_database=False)
        
        print(f"开始{mode_name}深度爬取测试...")
        print(f"参数: 最大深度=1, 最大文章数=10")
        
        start_time = time.time()
        articles = await crawler.deep_crawl_from_today(
            max_depth=1,
            max_articles=10,
            fetch_full_content=False,  # 不获取完整内容以加快测试
            fast_mode=fast_mode
        )
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"\n{mode_name}深度爬取完成:")
        print(f"  获取文章数量: {len(articles)}")
        print(f"  总耗时: {duration:.2f} 秒")
        if articles:
            print(f"  平均速度: {len(articles)/duration:.2f} 文章/秒")
        
        if articles:
            print(f"  文章示例:")
            for i, article in enumerate(articles[:3], 1):
                print(f"    {i}. {article.get('title', 'N/A')[:40]}...")
        
        return len(articles), duration
        
    except Exception as e:
        print(f"❌ {mode_name}深度爬取测试失败: {e}")
        return 0, 0

async def performance_comparison():
    """性能对比测试"""
    print("🚀 开始性能对比测试")
    print("对比普通模式和快速模式的性能差异")
    print()
    
    # 1. 测试链接提取速度
    link_count, link_duration = await test_link_extraction_speed()
    
    # 2. 测试批次爬取速度对比
    normal_success, normal_duration = await test_batch_crawl_speed(fast_mode=False)
    fast_success, fast_duration = await test_batch_crawl_speed(fast_mode=True)
    
    # 3. 测试深度爬取速度对比
    normal_articles, normal_deep_duration = await test_mini_deep_crawl(fast_mode=False)
    fast_articles, fast_deep_duration = await test_mini_deep_crawl(fast_mode=True)
    
    # 输出对比结果
    print("\n" + "=" * 60)
    print("性能对比结果汇总")
    print("=" * 60)
    
    print(f"\n📊 链接提取性能:")
    print(f"  提取链接数量: {link_count}")
    print(f"  耗时: {link_duration:.2f} 秒")
    
    print(f"\n📊 批次爬取性能对比:")
    print(f"  普通模式: {normal_success} 成功, {normal_duration:.2f} 秒")
    print(f"  快速模式: {fast_success} 成功, {fast_duration:.2f} 秒")
    if normal_duration > 0 and fast_duration > 0:
        speedup = normal_duration / fast_duration
        print(f"  速度提升: {speedup:.2f}x")
    
    print(f"\n📊 深度爬取性能对比:")
    print(f"  普通模式: {normal_articles} 文章, {normal_deep_duration:.2f} 秒")
    print(f"  快速模式: {fast_articles} 文章, {fast_deep_duration:.2f} 秒")
    if normal_deep_duration > 0 and fast_deep_duration > 0:
        deep_speedup = normal_deep_duration / fast_deep_duration
        print(f"  速度提升: {deep_speedup:.2f}x")
    
    print(f"\n💡 优化效果总结:")
    print(f"  - 并发数量: 普通模式 10 → 快速模式 20")
    print(f"  - 页面超时: 30秒 → 20秒")
    print(f"  - JavaScript等待: 3秒 → 1.5秒")
    print(f"  - 批次大小: 50 → 100")
    print(f"  - 链接处理限制: 无限制 → 200个")
    
    # 给出使用建议
    print(f"\n🎯 使用建议:")
    if fast_duration > 0 and normal_duration > 0:
        if fast_duration < normal_duration * 0.8:
            print(f"  ✅ 快速模式显著提升性能，建议使用 --fast-mode")
        else:
            print(f"  ⚠️  快速模式提升有限，可根据需要选择")
    
    print(f"\n📝 命令示例:")
    print(f"  # 普通深度爬取")
    print(f"  python main_advanced.py --deep-crawl")
    print(f"  ")
    print(f"  # 快速深度爬取")
    print(f"  python main_advanced.py --deep-crawl --fast-mode")
    print(f"  ")
    print(f"  # 快速大量爬取")
    print(f"  python main_advanced.py --deep-crawl --fast-mode --max-articles 5000")

async def main():
    """主测试函数"""
    print("开始爬取速度优化测试")
    print("目标: 验证快速模式的性能提升效果")
    print()
    
    await performance_comparison()

if __name__ == "__main__":
    asyncio.run(main())
