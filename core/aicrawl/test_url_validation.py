#!/usr/bin/env python3
"""
测试URL验证功能
验证只保留文章页面格式的链接
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_validation():
    """测试URL验证功能"""
    print("=" * 60)
    print("测试URL验证功能")
    print("=" * 60)
    
    try:
        from database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试用例
        test_urls = [
            # 有效的文章页面URL
            ("https://xueqiu.com/3631818965/349019806", True, "文章页面"),
            ("https://xueqiu.com/1234567890/123456789", True, "文章页面"),
            ("https://xueqiu.com/9999999999/999999999", True, "文章页面"),
            
            # 无效的个股页面URL（应该被过滤）
            ("https://xueqiu.com/S/SH688521", False, "个股页面"),
            ("https://xueqiu.com/S/SZ000001", False, "个股页面"),
            
            # 其他无效URL
            ("https://xueqiu.com", False, "首页"),
            ("https://xueqiu.com/today", False, "今日页面"),
            ("https://xueqiu.com/hq", False, "行情页面"),
            ("https://xueqiu.com/u/1234567890", False, "用户页面"),
            ("", False, "空URL"),
            ("#/", False, "锚点URL"),
            ("https://other.com/123/456", False, "其他网站"),
            ("https://xueqiu.com/123", False, "格式不完整"),
            ("https://xueqiu.com/abc/def", False, "非数字格式"),
        ]
        
        passed = 0
        total = len(test_urls)
        
        print(f"开始测试 {total} 个URL...")
        print()
        
        for url, expected, description in test_urls:
            result = db_manager._is_valid_url(url)
            status = "✅ 通过" if result == expected else "❌ 失败"
            expected_text = "保留" if expected else "过滤"
            result_text = "保留" if result else "过滤"
            
            print(f"{status} {description}")
            print(f"  URL: {url}")
            print(f"  预期: {expected_text}, 实际: {result_text}")
            
            if result == expected:
                passed += 1
            else:
                print(f"  ⚠️  测试失败！")
            print()
        
        print("=" * 60)
        print(f"测试结果: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("🎉 所有URL验证测试通过！")
            print("✅ 只有文章页面格式的URL会被保留")
            print("✅ 个股页面和其他格式的URL会被过滤")
        else:
            print("⚠️  部分测试失败，请检查验证逻辑")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_conversion():
    """测试数据转换功能"""
    print("\n" + "=" * 60)
    print("测试数据转换功能")
    print("=" * 60)
    
    try:
        from database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试数据
        test_articles = [
            {
                'title': '有效文章1',
                'url': 'https://xueqiu.com/3631818965/349019806',
                'content': '这是一篇有效的文章内容',
                'publish_time': '2024-01-01 10:00:00'
            },
            {
                'title': '无效个股页面',
                'url': 'https://xueqiu.com/S/SH688521',
                'content': '这是个股页面，应该被过滤',
                'publish_time': '2024-01-01 11:00:00'
            },
            {
                'title': '有效文章2',
                'url': 'https://xueqiu.com/1234567890/123456789',
                'content': '这是另一篇有效的文章内容',
                'publish_time': '2024-01-01 12:00:00'
            },
            {
                'title': '无效URL',
                'url': 'https://xueqiu.com/today',
                'content': '这是无效URL，应该被过滤',
                'publish_time': '2024-01-01 13:00:00'
            }
        ]
        
        print(f"测试 {len(test_articles)} 篇文章的数据转换...")
        print()
        
        valid_count = 0
        invalid_count = 0
        
        for i, article in enumerate(test_articles, 1):
            print(f"文章 {i}: {article['title']}")
            print(f"  URL: {article['url']}")
            
            converted = db_manager._convert_to_db_model(article)
            
            if converted:
                print(f"  ✅ 转换成功 - 将被保存到数据库")
                print(f"  mediaId: {converted['mediaId'][:20]}...")
                print(f"  source: {converted['source']}")
                valid_count += 1
            else:
                print(f"  ❌ 转换失败 - 被过滤，不会保存")
                invalid_count += 1
            print()
        
        print("=" * 60)
        print(f"转换结果: {valid_count} 篇有效，{invalid_count} 篇被过滤")
        
        # 验证预期结果
        expected_valid = 2  # 只有两篇文章页面格式的URL
        expected_invalid = 2  # 两篇无效URL
        
        if valid_count == expected_valid and invalid_count == expected_invalid:
            print("🎉 数据转换测试通过！")
            print("✅ 文章页面格式的URL正确转换")
            print("✅ 无效URL正确过滤")
            return True
        else:
            print("⚠️  数据转换测试失败")
            print(f"预期: {expected_valid} 篇有效，{expected_invalid} 篇无效")
            print(f"实际: {valid_count} 篇有效，{invalid_count} 篇无效")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试URL验证功能")
    print("目标: 只保留文章页面格式的链接")
    print()
    
    # 运行测试
    test1_result = test_url_validation()
    test2_result = test_url_validation()
    
    print("\n" + "=" * 60)
    print("总体测试结果")
    print("=" * 60)
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！")
        print()
        print("📝 URL验证规则:")
        print("  ✅ 保留: https://xueqiu.com/数字/数字 (文章页面)")
        print("  ❌ 过滤: https://xueqiu.com/S/股票代码 (个股页面)")
        print("  ❌ 过滤: 其他所有格式的URL")
        print()
        print("📝 这样可以确保只有真正的文章内容被保存到数据库")
    else:
        print("⚠️  部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
