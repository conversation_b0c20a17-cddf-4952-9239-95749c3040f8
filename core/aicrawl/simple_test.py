#!/usr/bin/env python3
"""
简化的Media模型测试
验证基本功能是否正常
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    try:
        import database
        print("✅ database模块导入成功")
        
        # 检查Media模型
        if hasattr(database, 'Media'):
            print("✅ Media模型存在")
        else:
            print("❌ Media模型不存在")
            return False
            
        # 检查DatabaseManager
        if hasattr(database, 'DatabaseManager'):
            print("✅ DatabaseManager类存在")
        else:
            print("❌ DatabaseManager类不存在")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_media_model():
    """测试Media模型结构"""
    print("\n" + "=" * 50)
    print("测试Media模型结构")
    print("=" * 50)
    
    try:
        from database import Media
        
        # 检查字段
        expected_fields = ['id', 'mediaId', 'source', 'title', 'url', 'createTime', 'content', 'important']
        
        for field in expected_fields:
            if hasattr(Media, field):
                print(f"✅ 字段 {field} 存在")
            else:
                print(f"❌ 字段 {field} 不存在")
                return False
        
        print("✅ Media模型结构正确")
        return True
        
    except Exception as e:
        print(f"❌ Media模型测试失败: {e}")
        return False

def test_database_manager():
    """测试DatabaseManager基本功能"""
    print("\n" + "=" * 50)
    print("测试DatabaseManager基本功能")
    print("=" * 50)
    
    try:
        from database import DatabaseManager
        
        # 创建实例（不连接数据库）
        db_manager = DatabaseManager()
        print("✅ DatabaseManager实例创建成功")
        
        # 测试数据转换功能
        test_article = {
            'title': '测试文章',
            'url': 'https://test.com/article1',
            'content': '这是测试内容',
            'publish_time': '2024-01-01 10:00:00',
            'important': True
        }
        
        converted = db_manager._convert_to_db_model(test_article)
        
        if converted:
            print("✅ 数据转换功能正常")
            print(f"  转换结果: {list(converted.keys())}")
            
            # 检查必要字段
            required_fields = ['mediaId', 'source', 'title', 'url', 'createTime', 'content', 'important']
            for field in required_fields:
                if field in converted:
                    print(f"  ✅ {field}: {converted[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
                    return False
        else:
            print("❌ 数据转换失败")
            return False
        
        print("✅ DatabaseManager基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ DatabaseManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_constraints():
    """测试字段长度限制"""
    print("\n" + "=" * 50)
    print("测试字段长度限制")
    print("=" * 50)
    
    try:
        from database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试超长字段
        test_article = {
            'title': 'A' * 200,  # 超过120字符
            'url': 'https://test.com/' + 'B' * 300,  # 超过200字符
            'content': 'C' * 1000,  # 长内容
            'publish_time': '2024-01-01 12:34:56',  # 超过10字符
        }
        
        converted = db_manager._convert_to_db_model(test_article)
        
        if converted:
            # 检查长度限制
            checks = [
                ('title', 120, len(converted['title'])),
                ('url', 200, len(converted['url'])),
                ('createTime', 10, len(converted['createTime'])),
                ('source', 8, len(converted['source']))
            ]
            
            all_passed = True
            for field, limit, actual in checks:
                if actual <= limit:
                    print(f"✅ {field}: {actual}/{limit} 字符")
                else:
                    print(f"❌ {field}: {actual}/{limit} 字符 (超出限制)")
                    all_passed = False
            
            if all_passed:
                print("✅ 字段长度限制正确")
                return True
            else:
                print("❌ 部分字段长度限制失败")
                return False
        else:
            print("❌ 数据转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 字段约束测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始简化测试")
    
    tests = [
        ("模块导入", test_imports),
        ("Media模型结构", test_media_model),
        ("DatabaseManager功能", test_database_manager),
        ("字段约束", test_field_constraints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 基本功能测试通过！")
        print("📝 说明: aicrawl已成功修改为使用Media模型")
        print("📝 主要变更:")
        print("   - XueqiuArticle → Media模型")
        print("   - 字段映射已调整")
        print("   - 数据库配置已统一")
    else:
        print("⚠️  部分测试失败")

if __name__ == "__main__":
    main()
