"""
独立的数据库存储模块
用于保存爬取的雪球文章数据
"""

import os
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from sqlalchemy import create_engine, Column, String, Integer, Text, Boolean, DateTime
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.dialects.mysql import insert
    from sqlalchemy.exc import SQLAlchemyError
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

logger = logging.getLogger(__name__)

# 创建基类
Base = declarative_base()

class XueqiuArticle(Base):
    """雪球文章数据模型"""
    __tablename__ = 'xueqiu_articles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    article_id = Column(String(64), unique=True, nullable=False, comment='文章唯一ID')
    source = Column(String(16), default='xueqiu', comment='数据源')
    title = Column(String(255), nullable=False, comment='文章标题')
    author = Column(String(64), comment='作者')
    url = Column(String(512), comment='文章链接')
    content = Column(Text, comment='文章内容')
    content_fetched = Column(Boolean, default=False, comment='是否获取了完整内容')
    publish_time = Column(String(32), comment='发布时间')
    crawl_time = Column(String(32), comment='爬取时间')
    source_page = Column(String(128), comment='来源页面')
    article_type = Column(String(32), comment='文章类型')
    view_count = Column(String(16), comment='浏览数')
    like_count = Column(String(16), comment='点赞数')
    comment_count = Column(String(16), comment='评论数')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or self._get_default_database_url()
        self.engine = None
        self.SessionLocal = None
        self.initialized = False
        
        if SQLALCHEMY_AVAILABLE:
            self._initialize_database()
        else:
            logger.warning("SQLAlchemy不可用，数据库功能将被禁用")
    
    def _get_default_database_url(self) -> str:
        """获取默认数据库URL"""
        # 从环境变量获取数据库配置
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '3306')
        db_user = os.getenv('DB_USER', 'root')
        db_password = os.getenv('DB_PASSWORD', '')
        db_name = os.getenv('DB_NAME', 'xueqiu_crawler')
        
        return f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    
    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 创建引擎
            self.engine = create_engine(
                self.database_url,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # 设置为True可以看到SQL语句
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(bind=self.engine)
            
            # 创建表
            Base.metadata.create_all(self.engine)
            
            self.initialized = True
            logger.info("数据库初始化成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.initialized = False
    
    def get_session(self):
        """获取数据库会话"""
        if not self.initialized:
            return None
        return self.SessionLocal()
    
    def save_articles(self, articles: List[Dict[str, Any]]) -> Dict[str, int]:
        """批量保存文章到数据库"""
        if not self.initialized:
            logger.warning("数据库未初始化，无法保存数据")
            return {'success': 0, 'failed': 0, 'skipped': 0}
        
        session = self.get_session()
        if not session:
            return {'success': 0, 'failed': 0, 'skipped': 0}
        
        stats = {'success': 0, 'failed': 0, 'skipped': 0}
        
        try:
            for article in articles:
                try:
                    # 转换为数据库模型数据
                    article_data = self._convert_to_db_model(article)
                    if not article_data:
                        stats['skipped'] += 1
                        continue
                    
                    # 使用MySQL的ON DUPLICATE KEY UPDATE
                    insert_stmt = insert(XueqiuArticle).values(**article_data)
                    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(
                        title=article_data['title'],
                        content=article_data['content'],
                        content_fetched=article_data['content_fetched'],
                        updated_at=datetime.now()
                    )
                    
                    session.execute(on_duplicate_key_stmt)
                    stats['success'] += 1
                    
                except Exception as e:
                    logger.error(f"保存单篇文章失败: {e}")
                    stats['failed'] += 1
                    continue
            
            # 提交事务
            session.commit()
            logger.info(f"数据库保存完成: 成功 {stats['success']} 篇，失败 {stats['failed']} 篇，跳过 {stats['skipped']} 篇")
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库事务失败: {e}")
            stats['failed'] = len(articles)
            stats['success'] = 0
            
        except Exception as e:
            session.rollback()
            logger.error(f"数据库保存异常: {e}")
            stats['failed'] = len(articles)
            stats['success'] = 0
            
        finally:
            session.close()
        
        return stats
    
    def _convert_to_db_model(self, article: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """将文章数据转换为数据库模型格式"""
        try:
            # 生成唯一的article_id
            article_id = self._generate_article_id(article)
            if not article_id:
                return None
            
            # 处理时间字段
            publish_time = article.get('publish_time', '')
            crawl_time = article.get('crawl_time', time.strftime('%Y-%m-%d %H:%M:%S'))
            
            # 构建数据库记录
            db_data = {
                'article_id': article_id,
                'source': 'xueqiu',
                'title': (article.get('title', '') or '')[:255],  # 限制长度
                'author': (article.get('author', '') or '')[:64],
                'url': (article.get('url', '') or '')[:512],
                'content': article.get('content', '') or '',
                'content_fetched': article.get('content_fetched', False),
                'publish_time': publish_time[:32] if publish_time else '',
                'crawl_time': crawl_time[:32],
                'source_page': (article.get('source_page', '') or '')[:128],
                'article_type': (article.get('type', '') or '')[:32],
                'view_count': str(article.get('view_count', '') or '')[:16],
                'like_count': str(article.get('like_count', '') or '')[:16],
                'comment_count': str(article.get('comment_count', '') or '')[:16],
            }
            
            return db_data
            
        except Exception as e:
            logger.error(f"转换文章数据失败: {e}")
            return None
    
    def _generate_article_id(self, article: Dict[str, Any]) -> Optional[str]:
        """生成文章唯一ID"""
        try:
            import hashlib
            
            # 优先使用URL作为唯一标识
            url = article.get('url', '')
            if url and url not in ['#/', '#', '']:
                return hashlib.md5(url.encode()).hexdigest()
            
            # 如果没有URL，使用标题+作者+时间
            title = article.get('title', '')
            author = article.get('author', '')
            time_str = article.get('publish_time', '') or article.get('crawl_time', '')
            
            if title:
                content_for_id = f"{title}{author}{time_str}"
                return hashlib.md5(content_for_id.encode()).hexdigest()
            
            return None
            
        except Exception as e:
            logger.error(f"生成文章ID失败: {e}")
            return None
    
    def get_article_count(self) -> int:
        """获取文章总数"""
        if not self.initialized:
            return 0
        
        session = self.get_session()
        if not session:
            return 0
        
        try:
            count = session.query(XueqiuArticle).count()
            return count
        except Exception as e:
            logger.error(f"获取文章数量失败: {e}")
            return 0
        finally:
            session.close()
    
    def get_recent_articles(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的文章"""
        if not self.initialized:
            return []
        
        session = self.get_session()
        if not session:
            return []
        
        try:
            articles = session.query(XueqiuArticle)\
                .order_by(XueqiuArticle.created_at.desc())\
                .limit(limit)\
                .all()
            
            result = []
            for article in articles:
                result.append({
                    'id': article.id,
                    'article_id': article.article_id,
                    'title': article.title,
                    'author': article.author,
                    'url': article.url,
                    'content_fetched': article.content_fetched,
                    'publish_time': article.publish_time,
                    'crawl_time': article.crawl_time,
                    'created_at': article.created_at.strftime('%Y-%m-%d %H:%M:%S') if article.created_at else ''
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取最近文章失败: {e}")
            return []
        finally:
            session.close()

# 全局数据库管理器实例
db_manager = None

def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager

def init_database(database_url: str = None) -> bool:
    """初始化数据库"""
    global db_manager
    try:
        db_manager = DatabaseManager(database_url)
        return db_manager.initialized
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False
