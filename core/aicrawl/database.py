"""
独立的数据库存储模块
用于保存爬取的雪球文章数据
使用Media模型结构
"""

import os
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from sqlalchemy import create_engine, Column, String, Integer, Text, Boolean, DateTime
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.dialects.mysql import insert
    from sqlalchemy.exc import SQLAlchemyError
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

logger = logging.getLogger(__name__)

# 创建基类
Base = declarative_base()

class Media(Base):
    """媒体文章数据模型 - 复制自crawl模块"""
    __tablename__ = 'media'

    id = Column(Integer, primary_key=True)
    mediaId = Column(String(40))
    source = Column(String(8))
    title = Column(String(120))
    url = Column(String(200))
    createTime = Column(String(10))
    content = Column(Text)
    important = Column(Boolean)

class DatabaseManager:
    """数据库管理器 - 使用Media模型"""

    def __init__(self, database_url: str = None):
        self.database_url = database_url or self._get_default_database_url()
        self.engine = None
        self.SessionLocal = None
        self.initialized = False

        if SQLALCHEMY_AVAILABLE:
            self._initialize_database()
        else:
            logger.warning("SQLAlchemy不可用，数据库功能将被禁用")

    def _get_default_database_url(self) -> str:
        """获取默认数据库URL - 使用与crawl模块相同的配置"""
        # 使用与crawl模块完全相同的环境变量配置
        host = os.getenv('BB_MYSQL_HOST', 'localhost')
        db = os.getenv('BB_MYSQL_DBNAME', 'blackbear')
        user = os.getenv('BB_MYSQL_USER', 'root')
        passwd = os.getenv('BB_MYSQL_PASSWD', '')
        port = int(os.getenv('BB_MYSQL_PORT', 3306))

        # 使用与crawl模块相同的连接字符串格式
        return f'mysql+mysqlconnector://{user}:{passwd}@{host}:{port}/{db}'

    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 创建引擎
            self.engine = create_engine(
                self.database_url,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # 设置为True可以看到SQL语句
            )

            # 创建会话工厂
            self.SessionLocal = sessionmaker(bind=self.engine)

            # 创建表
            Base.metadata.create_all(self.engine)

            self.initialized = True
            logger.info("数据库初始化成功")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.initialized = False

    def get_session(self):
        """获取数据库会话"""
        if not self.initialized:
            return None
        return self.SessionLocal()
    
    def save_articles(self, articles: List[Dict[str, Any]]) -> Dict[str, int]:
        """批量保存文章到数据库"""
        if not self.initialized:
            logger.warning("数据库未初始化，无法保存数据")
            return {'success': 0, 'failed': 0, 'skipped': 0}

        session = self.get_session()
        if not session:
            return {'success': 0, 'failed': 0, 'skipped': 0}

        stats = {'success': 0, 'failed': 0, 'skipped': 0, 'invalid_url': 0}

        try:
            for article in articles:
                try:
                    # 转换为数据库模型数据
                    article_data = self._convert_to_db_model(article)
                    if not article_data:
                        # 检查是否因为URL无效而跳过
                        url = article.get('url', '')
                        if url and not self._is_valid_url(url):
                            stats['invalid_url'] += 1
                        else:
                            stats['skipped'] += 1
                        continue

                    # 使用MySQL的ON DUPLICATE KEY UPDATE
                    insert_stmt = insert(Media).values(**article_data)
                    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(
                        title=article_data['title'],
                        content=article_data['content'],
                        important=article_data['important']
                    )

                    session.execute(on_duplicate_key_stmt)
                    stats['success'] += 1

                except Exception as e:
                    logger.error(f"保存单篇文章失败: {e}")
                    stats['failed'] += 1
                    continue

            # 提交事务
            session.commit()
            logger.info(f"数据库保存完成: 成功 {stats['success']} 篇，失败 {stats['failed']} 篇，跳过 {stats['skipped']} 篇，无效URL {stats['invalid_url']} 篇")

        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库事务失败: {e}")
            stats['failed'] = len(articles)
            stats['success'] = 0

        except Exception as e:
            session.rollback()
            logger.error(f"数据库保存异常: {e}")
            stats['failed'] = len(articles)
            stats['success'] = 0

        finally:
            session.close()

        return stats
    
    def _convert_to_db_model(self, article: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """将文章数据转换为Media模型格式"""
        try:
            # URL验证 - 只保留指定格式的链接
            url = article.get('url', '')
            if not self._is_valid_url(url):
                logger.debug(f"跳过无效URL: {url}")
                return None

            # 生成唯一的mediaId
            media_id = self._generate_media_id(article)
            if not media_id:
                return None

            # 处理时间字段
            create_time = article.get('publish_time', '') or article.get('crawl_time', '') or time.strftime('%Y-%m-%d')
            # 只保留日期部分，格式为YYYY-MM-DD
            if len(create_time) > 10:
                create_time = create_time[:10]

            # 构建数据库记录 - 适配Media模型字段
            db_data = {
                'mediaId': media_id,
                'source': '22222',  # 限制为8个字符
                'title': (article.get('title', '') or '')[:120],  # 限制长度为120
                'url': url[:200],  # 限制长度为200
                'createTime': create_time[:10],  # 限制长度为10
                'content': article.get('content', '') or '',
                'important': article.get('important', False) or article.get('content_fetched', False),
            }

            return db_data

        except Exception as e:
            logger.error(f"转换文章数据失败: {e}")
            return None
    
    def _is_valid_url(self, url: str) -> bool:
        """验证URL是否为有效的雪球链接格式"""
        if not url or url in ['#/', '#', '']:
            return False

        import re

        # 定义有效的URL格式
        # 格式1: 个股页面 - https://xueqiu.com/S/SH688521
        stock_pattern = r'^https://xueqiu\.com/S/[A-Z]{2}\d{6}$'

        # 格式2: 文章页面 - https://xueqiu.com/3631818965/349019806
        article_pattern = r'^https://xueqiu\.com/\d+/\d+$'

        # 检查是否匹配任一格式
        if re.match(stock_pattern, url) or re.match(article_pattern, url):
            return True

        logger.debug(f"URL格式不匹配: {url}")
        return False

    def _generate_media_id(self, article: Dict[str, Any]) -> Optional[str]:
        """生成媒体唯一ID"""
        try:
            import hashlib

            # 优先使用URL作为唯一标识
            url = article.get('url', '')
            if url and url not in ['#/', '#', '']:
                # 生成40位以内的ID
                return hashlib.md5(url.encode()).hexdigest()[:40]

            # 如果没有URL，使用标题+作者+时间
            title = article.get('title', '')
            author = article.get('author', '')
            time_str = article.get('publish_time', '') or article.get('crawl_time', '')

            if title:
                content_for_id = f"{title}{author}{time_str}"
                return hashlib.md5(content_for_id.encode()).hexdigest()[:40]

            return None

        except Exception as e:
            logger.error(f"生成媒体ID失败: {e}")
            return None
    
    def get_article_count(self) -> int:
        """获取媒体文章总数"""
        if not self.initialized:
            return 0

        session = self.get_session()
        if not session:
            return 0

        try:
            count = session.query(Media).filter(Media.source == 'xueqiu').count()
            return count
        except Exception as e:
            logger.error(f"获取文章数量失败: {e}")
            return 0
        finally:
            session.close()

    def get_recent_articles(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的媒体文章"""
        if not self.initialized:
            return []

        session = self.get_session()
        if not session:
            return []

        try:
            articles = session.query(Media)\
                .filter(Media.source == 'xueqiu')\
                .order_by(Media.id.desc())\
                .limit(limit)\
                .all()

            result = []
            for article in articles:
                result.append({
                    'id': article.id,
                    'mediaId': article.mediaId,
                    'title': article.title,
                    'source': article.source,
                    'url': article.url,
                    'content': article.content[:200] + '...' if article.content and len(article.content) > 200 else article.content,
                    'createTime': article.createTime,
                    'important': article.important
                })

            return result

        except Exception as e:
            logger.error(f"获取最近文章失败: {e}")
            return []
        finally:
            session.close()

# 全局数据库管理器实例
db_manager = None

def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager

def init_database(database_url: str = None) -> bool:
    """初始化数据库"""
    global db_manager
    try:
        db_manager = DatabaseManager(database_url)
        return db_manager.initialized
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False
