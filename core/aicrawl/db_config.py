"""
数据库配置文件
"""

import os
from typing import Dict, Any

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        return {
            # MySQL配置 - 使用与crawl模块相同的配置
            'mysql': {
                'host': os.getenv('BB_MYSQL_HOST', os.getenv('MYSQL_HOST', 'localhost')),
                'port': int(os.getenv('BB_MYSQL_PORT', os.getenv('MYSQL_PORT', 3306))),
                'user': os.getenv('BB_MYSQL_USER', os.getenv('MYSQL_USER', 'root')),
                'password': os.getenv('BB_MYSQL_PASSWD', os.getenv('MYSQL_PASSWORD', '')),
                'database': os.getenv('BB_MYSQL_DBNAME', os.getenv('MYSQL_DATABASE', 'blackbear')),
                'charset': 'utf8mb4',
                'pool_size': 10,
                'pool_recycle': 3600,
                'pool_pre_ping': True
            },
            
            # SQLite配置（备用）
            'sqlite': {
                'database': os.getenv('SQLITE_DATABASE', 'blackbear.db'),
                'check_same_thread': False
            },
            
            # 默认使用的数据库类型
            'default_type': os.getenv('DATABASE_TYPE', 'mysql')
        }
    
    def get_mysql_url(self) -> str:
        """获取MySQL连接URL - 使用与crawl模块相同的连接器"""
        mysql_config = self.config['mysql']
        return (f"mysql+mysqlconnector://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}"
                f"/{mysql_config['database']}")
    
    def get_sqlite_url(self) -> str:
        """获取SQLite连接URL"""
        sqlite_config = self.config['sqlite']
        db_path = os.path.join(os.path.dirname(__file__), sqlite_config['database'])
        return f"sqlite:///{db_path}"
    
    def get_database_url(self, db_type: str = None) -> str:
        """获取数据库连接URL"""
        db_type = db_type or self.config['default_type']
        
        if db_type == 'mysql':
            return self.get_mysql_url()
        elif db_type == 'sqlite':
            return self.get_sqlite_url()
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def get_engine_options(self, db_type: str = None) -> Dict[str, Any]:
        """获取数据库引擎选项"""
        db_type = db_type or self.config['default_type']
        
        if db_type == 'mysql':
            mysql_config = self.config['mysql']
            return {
                'pool_size': mysql_config['pool_size'],
                'pool_recycle': mysql_config['pool_recycle'],
                'pool_pre_ping': mysql_config['pool_pre_ping'],
                'echo': False
            }
        elif db_type == 'sqlite':
            return {
                'echo': False,
                'connect_args': {'check_same_thread': False}
            }
        else:
            return {'echo': False}

# 全局配置实例
db_config = DatabaseConfig()

# 便捷函数
def get_database_url(db_type: str = None) -> str:
    """获取数据库连接URL"""
    return db_config.get_database_url(db_type)

def get_engine_options(db_type: str = None) -> Dict[str, Any]:
    """获取数据库引擎选项"""
    return db_config.get_engine_options(db_type)
